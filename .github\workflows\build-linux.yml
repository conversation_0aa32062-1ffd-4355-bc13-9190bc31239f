name: Build Linux

on:
  workflow_call:
  workflow_dispatch:
  push:
    branches: [main]

jobs:
  build:
    name: Build Linux x86_64
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            pkg-config \
            libwebkit2gtk-4.1-dev \
            libgtk-3-dev \
            libssl-dev \
            libayatana-appindicator3-dev \
            librsvg2-dev
      
      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: x86_64-unknown-linux-gnu
      
      - name: Setup Rust cache
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: src-tauri
      
      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
      
      - name: Install dependencies
        run: bun install
      
      - name: Build Tauri app
        run: bun run tauri build --target x86_64-unknown-linux-gnu
      
      - name: Create artifacts directory
        run: |
          mkdir -p dist/linux-x86_64
          cp src-tauri/target/x86_64-unknown-linux-gnu/release/bundle/deb/*.deb dist/linux-x86_64/ || true
          cp src-tauri/target/x86_64-unknown-linux-gnu/release/bundle/appimage/*.AppImage dist/linux-x86_64/ || true
          
          # Generate checksums
          cd dist/linux-x86_64
          sha256sum * > checksums.txt
      
      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: linux-x86_64
          path: dist/linux-x86_64/*
